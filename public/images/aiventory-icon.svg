<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Primary gradient for the main elements -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6366f1" />
      <stop offset="50%" stop-color="#7c3aed" />
      <stop offset="100%" stop-color="#8b5cf6" />
    </linearGradient>
    
    <!-- Secondary gradient for accents -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f59e0b" />
      <stop offset="100%" stop-color="#f97316" />
    </linearGradient>
    
    <!-- Subtle background gradient -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8fafc" />
      <stop offset="100%" stop-color="#f1f5f9" />
    </linearGradient>
  </defs>

  <!-- Background circle with subtle gradient -->
  <circle cx="20" cy="20" r="19" fill="url(#backgroundGradient)" stroke="#e2e8f0" stroke-width="1"/>
  
  <!-- Main icon: Stylized compass/navigation symbol representing discovery -->
  <g transform="translate(20, 20)">
    <!-- Outer compass ring -->
    <circle cx="0" cy="0" r="12" fill="none" stroke="url(#primaryGradient)" stroke-width="1.5" opacity="0.3"/>
    
    <!-- Inner compass elements -->
    <g>
      <!-- North pointer (AI symbol) -->
      <path d="M0,-8 L2,-4 L0,-2 L-2,-4 Z" fill="url(#primaryGradient)"/>
      
      <!-- South pointer -->
      <path d="M0,8 L2,4 L0,2 L-2,4 Z" fill="url(#primaryGradient)" opacity="0.6"/>
      
      <!-- East pointer -->
      <path d="M8,0 L4,2 L2,0 L4,-2 Z" fill="url(#accentGradient)" opacity="0.8"/>
      
      <!-- West pointer -->
      <path d="M-8,0 L-4,2 L-2,0 L-4,-2 Z" fill="url(#accentGradient)" opacity="0.6"/>
    </g>
    
    <!-- Center dot representing current position/focus -->
    <circle cx="0" cy="0" r="2" fill="url(#primaryGradient)"/>
    
    <!-- Subtle orbital rings suggesting AI/tech -->
    <circle cx="0" cy="0" r="6" fill="none" stroke="url(#primaryGradient)" stroke-width="0.5" opacity="0.2" stroke-dasharray="2,2"/>
    <circle cx="0" cy="0" r="9" fill="none" stroke="url(#accentGradient)" stroke-width="0.5" opacity="0.15" stroke-dasharray="3,3"/>
  </g>
  
  <!-- Subtle sparkle elements suggesting AI intelligence -->
  <g opacity="0.4">
    <path d="M8,8 L9,10 L11,9 L9,8 Z" fill="url(#accentGradient)"/>
    <path d="M32,12 L33,14 L35,13 L33,12 Z" fill="url(#primaryGradient)"/>
    <path d="M6,30 L7,32 L9,31 L7,30 Z" fill="url(#primaryGradient)"/>
  </g>
</svg>
