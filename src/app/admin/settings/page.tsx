"use client";

import React, { useEffect, useState } from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Brain,
  Database,
  Mail,
  Shield
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from '@/contexts/AuthContext';
import { adminGetSettings, adminUpdateSetting } from '@/services/api';

interface AppSetting {
  key: string;
  value: string;
  description?: string;
  category: 'AI' | 'Database' | 'Email' | 'Security' | 'General';
  type: 'text' | 'select' | 'boolean' | 'number';
  options?: string[];
}

const settingCategories = {
  AI: { icon: Brain, color: 'text-purple-600' },
  Database: { icon: Database, color: 'text-blue-600' },
  Email: { icon: Mail, color: 'text-green-600' },
  Security: { icon: Shield, color: 'text-red-600' },
  General: { icon: Settings, color: 'text-gray-600' },
};

export default function AdminSettings() {
  const { session } = useAuth();
  const [settings, setSettings] = useState<AppSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [savedSettings, setSavedSettings] = useState<Set<string>>(new Set());

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await adminGetSettings(session?.access_token!);
      
      // Mock settings data since the API might not return the exact structure
      const mockSettings: AppSetting[] = [
        {
          key: 'CURRENT_LLM_PROVIDER',
          value: 'OPENAI',
          description: 'The AI provider used for chat and recommendations',
          category: 'AI',
          type: 'select',
          options: ['OPENAI', 'ANTHROPIC', 'GOOGLE', 'AZURE_OPENAI']
        },
        {
          key: 'OPENAI_API_KEY',
          value: 'sk-...',
          description: 'OpenAI API key for AI services',
          category: 'AI',
          type: 'text'
        },
        {
          key: 'MAX_CHAT_HISTORY',
          value: '50',
          description: 'Maximum number of chat messages to keep in history',
          category: 'AI',
          type: 'number'
        },
        {
          key: 'ENABLE_USER_REGISTRATION',
          value: 'true',
          description: 'Allow new users to register accounts',
          category: 'Security',
          type: 'boolean'
        },
        {
          key: 'REQUIRE_EMAIL_VERIFICATION',
          value: 'true',
          description: 'Require email verification for new accounts',
          category: 'Security',
          type: 'boolean'
        },
        {
          key: 'SMTP_HOST',
          value: 'smtp.gmail.com',
          description: 'SMTP server for sending emails',
          category: 'Email',
          type: 'text'
        },
        {
          key: 'SMTP_PORT',
          value: '587',
          description: 'SMTP server port',
          category: 'Email',
          type: 'number'
        },
        {
          key: 'DATABASE_BACKUP_FREQUENCY',
          value: 'daily',
          description: 'How often to backup the database',
          category: 'Database',
          type: 'select',
          options: ['hourly', 'daily', 'weekly', 'monthly']
        },
        {
          key: 'PLATFORM_NAME',
          value: 'AIventory',
          description: 'Name of the platform displayed to users',
          category: 'General',
          type: 'text'
        },
        {
          key: 'MAINTENANCE_MODE',
          value: 'false',
          description: 'Enable maintenance mode to restrict access',
          category: 'General',
          type: 'boolean'
        }
      ];

      setSettings(mockSettings);
    } catch (error) {
      console.error('Error fetching settings:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.access_token) {
      fetchSettings();
    }
  }, [session]);

  const handleSettingUpdate = async (key: string, value: string) => {
    if (!session?.access_token) return;

    try {
      setSaving(key);
      await adminUpdateSetting(key, value, session.access_token);
      
      // Update local state
      setSettings(prev => prev.map(setting => 
        setting.key === key 
          ? { ...setting, value }
          : setting
      ));

      // Show success indicator
      setSavedSettings(prev => new Set([...prev, key]));
      setTimeout(() => {
        setSavedSettings(prev => {
          const newSet = new Set(prev);
          newSet.delete(key);
          return newSet;
        });
      }, 2000);
    } catch (error) {
      console.error('Error updating setting:', error);
      alert('Failed to update setting');
    } finally {
      setSaving(null);
    }
  };

  const SettingInput: React.FC<{ setting: AppSetting }> = ({ setting }) => {
    const [localValue, setLocalValue] = useState(setting.value);
    const hasChanged = localValue !== setting.value;
    const isSaved = savedSettings.has(setting.key);

    const handleSave = () => {
      if (hasChanged) {
        handleSettingUpdate(setting.key, localValue);
      }
    };

    const renderInput = () => {
      switch (setting.type) {
        case 'select':
          return (
            <Select value={localValue} onValueChange={setLocalValue}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {setting.options?.map(option => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        
        case 'boolean':
          return (
            <Select value={localValue} onValueChange={setLocalValue}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Enabled</SelectItem>
                <SelectItem value="false">Disabled</SelectItem>
              </SelectContent>
            </Select>
          );
        
        case 'number':
          return (
            <Input
              type="number"
              value={localValue}
              onChange={(e) => setLocalValue(e.target.value)}
            />
          );
        
        default:
          return (
            <Input
              type={setting.key.toLowerCase().includes('password') || setting.key.toLowerCase().includes('key') ? 'password' : 'text'}
              value={localValue}
              onChange={(e) => setLocalValue(e.target.value)}
            />
          );
      }
    };

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor={setting.key} className="text-sm font-medium">
            {setting.key.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <div className="flex items-center space-x-2">
            {isSaved && (
              <CheckCircle className="h-4 w-4 text-green-600" />
            )}
            {hasChanged && (
              <Button
                size="sm"
                onClick={handleSave}
                disabled={saving === setting.key}
              >
                {saving === setting.key ? (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                ) : (
                  <Save className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>
        {renderInput()}
        {setting.description && (
          <p className="text-xs text-gray-500">{setting.description}</p>
        )}
      </div>
    );
  };

  const groupedSettings = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = [];
    }
    acc[setting.category].push(setting);
    return acc;
  }, {} as Record<string, AppSetting[]>);

  return (
    <AdminRouteGuard requiredPermission="canManageSettings">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Application Settings</h1>
              <p className="mt-1 text-sm text-gray-500">
                Configure system-wide settings and preferences
              </p>
            </div>
            <Button
              variant="outline"
              onClick={fetchSettings}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              <p className="ml-3 text-sm text-gray-500">Loading settings...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {Object.entries(groupedSettings).map(([category, categorySettings]) => {
                const CategoryIcon = settingCategories[category as keyof typeof settingCategories]?.icon || Settings;
                const iconColor = settingCategories[category as keyof typeof settingCategories]?.color || 'text-gray-600';

                return (
                  <div key={category} className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <div className="flex items-center">
                        <CategoryIcon className={`h-5 w-5 mr-2 ${iconColor}`} />
                        <h3 className="text-lg font-medium text-gray-900">{category} Settings</h3>
                      </div>
                    </div>
                    <div className="px-6 py-4 space-y-6">
                      {categorySettings.map(setting => (
                        <SettingInput key={setting.key} setting={setting} />
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Warning notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  Important Notice
                </h3>
                <p className="mt-1 text-sm text-yellow-700">
                  Changes to these settings can affect the entire platform. Please ensure you understand 
                  the implications before making modifications. Some changes may require a system restart 
                  to take effect.
                </p>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
