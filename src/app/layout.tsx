import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Layout from "@/components/layout/Layout";
import { AuthProvider } from "@/contexts/AuthContext";
import { BookmarkProvider } from "@/contexts/BookmarkContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AIventory - The World's Most Comprehensive AI Resource Platform",
  description: "Discover and navigate the AI ecosystem with the world's most comprehensive AI resource platform. Find AI tools, courses, jobs, hardware, and more.",
  keywords: ["AI tools", "artificial intelligence", "AI resources", "AI platform", "AI discovery", "machine learning", "AI courses", "AI jobs"],
  authors: [{ name: "AI<PERSON>ory" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "AIventory",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    type: "website",
    url: "https://www.aiventory.io",
    title: "AIventory - The World's Most Comprehensive AI Resource Platform",
    description: "Discover and navigate the AI ecosystem with the world's most comprehensive AI resource platform. Find AI tools, courses, jobs, hardware, and more.",
    siteName: "AIventory",
    images: [
      {
        url: "https://www.aiventory.io/images/og-default.png",
        width: 1200,
        height: 630,
        alt: "AIventory - AI Resource Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AIventory - The World's Most Comprehensive AI Resource Platform",
    description: "Discover and navigate the AI ecosystem with the world's most comprehensive AI resource platform.",
    images: ["https://www.aiventory.io/images/og-image.png"],
    creator: "@aiventory",
  },
  alternates: {
    canonical: "https://www.aiventory.io",
  },
  metadataBase: new URL("https://www.aiventory.io"),
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <BookmarkProvider>
            <Layout>{children}</Layout>
          </BookmarkProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
