import React from 'react';
import { Metadata } from 'next';
import { getEntityBySlug } from '@/services/api';
import EntityPageClient from './EntityPageClient';

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  try {
    const { slug } = await params;
    const entity = await getEntityBySlug(slug);

    const title = entity.metaTitle || `${entity.name} | AIventory`;
    const description = entity.metaDescription || entity.shortDescription || entity.description || `Discover ${entity.name} on AIventory`;
    const url = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aiventory.io'}/entities/${entity.slug}`;
    const imageUrl = entity.logoUrl || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aiventory.io'}/images/og-default.png`;

    return {
      title,
      description,
      keywords: [
        entity.name,
        entity.entityType.name,
        ...(entity.categories || []).map(cat => cat.name),
        ...(entity.tags || []).map(tag => tag.name),
        ...(entity.features || []).map(feature => feature.name),
        'AI', 'artificial intelligence', 'tools', 'resources'
      ].join(', '),
      authors: [{ name: 'AIventory' }],
      creator: 'AIventory',
      publisher: 'AIventory',
      robots: {
        index: entity.status === 'ACTIVE',
        follow: true,
        googleBot: {
          index: entity.status === 'ACTIVE',
          follow: true,
        },
      },
      openGraph: {
        type: 'website',
        url,
        title,
        description,
        siteName: 'AIventory',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${entity.name} logo`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [imageUrl],
        creator: '@aiventory',
      },
      alternates: {
        canonical: url,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Entity Not Found | AIventory',
      description: 'The requested entity could not be found.',
    };
  }
}

export default async function EntitySlugPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  return <EntityPageClient slug={slug} />;
}
