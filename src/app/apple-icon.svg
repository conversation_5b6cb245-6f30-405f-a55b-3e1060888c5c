<svg viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6366f1" />
      <stop offset="50%" stop-color="#7c3aed" />
      <stop offset="100%" stop-color="#8b5cf6" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f59e0b" />
      <stop offset="100%" stop-color="#f97316" />
    </linearGradient>
    
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f8fafc" />
      <stop offset="100%" stop-color="#f1f5f9" />
    </linearGradient>
  </defs>

  <!-- Rounded square background for iOS -->
  <rect x="0" y="0" width="180" height="180" rx="40" ry="40" fill="url(#backgroundGradient)"/>
  
  <!-- Main compass icon scaled up -->
  <g transform="translate(90, 90)">
    <!-- Outer compass ring -->
    <circle cx="0" cy="0" r="60" fill="none" stroke="url(#primaryGradient)" stroke-width="6" opacity="0.3"/>
    
    <!-- Inner compass elements -->
    <g>
      <!-- North pointer (AI symbol) -->
      <path d="M0,-40 L10,-20 L0,-10 L-10,-20 Z" fill="url(#primaryGradient)"/>
      
      <!-- South pointer -->
      <path d="M0,40 L10,20 L0,10 L-10,20 Z" fill="url(#primaryGradient)" opacity="0.6"/>
      
      <!-- East pointer -->
      <path d="M40,0 L20,10 L10,0 L20,-10 Z" fill="url(#accentGradient)" opacity="0.8"/>
      
      <!-- West pointer -->
      <path d="M-40,0 L-20,10 L-10,0 L-20,-10 Z" fill="url(#accentGradient)" opacity="0.6"/>
    </g>
    
    <!-- Center dot representing current position/focus -->
    <circle cx="0" cy="0" r="8" fill="url(#primaryGradient)"/>
    
    <!-- Orbital rings -->
    <circle cx="0" cy="0" r="30" fill="none" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.2" stroke-dasharray="8,8"/>
    <circle cx="0" cy="0" r="45" fill="none" stroke="url(#accentGradient)" stroke-width="2" opacity="0.15" stroke-dasharray="12,12"/>
  </g>
  
  <!-- Subtle sparkle elements -->
  <g opacity="0.4">
    <path d="M40,40 L45,50 L55,45 L45,40 Z" fill="url(#accentGradient)"/>
    <path d="M140,50 L145,60 L155,55 L145,50 Z" fill="url(#primaryGradient)"/>
    <path d="M30,140 L35,150 L45,145 L35,140 Z" fill="url(#primaryGradient)"/>
  </g>
</svg>
