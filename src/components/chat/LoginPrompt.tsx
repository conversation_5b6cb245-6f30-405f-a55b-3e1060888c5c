'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Sparkles,
  MessageCircle,
  Zap,
  Target,
  Shield,
  ArrowRight,
  Bot,
  Search,
  BookOpen,
  Lightbulb
} from 'lucide-react';
import AIventoryLogo from '@/components/ui/AIventoryLogo';

const LoginPrompt: React.FC = () => {
  const features = [
    {
      icon: Bo<PERSON>,
      title: "AI-Powered Recommendations",
      description: "Get personalized tool suggestions based on your specific needs and requirements."
    },
    {
      icon: Search,
      title: "Smart Discovery",
      description: "Find the perfect AI tools from our curated database of thousands of resources."
    },
    {
      icon: BookOpen,
      title: "Learning Resources",
      description: "Discover courses, tutorials, and educational content to advance your AI knowledge."
    },
    {
      icon: Lightbulb,
      title: "Expert Insights",
      description: "Access detailed reviews, comparisons, and expert recommendations."
    }
  ];

  const sampleQuestions = [
    "What's the best AI tool for content creation?",
    "I need help with data analysis - what tools do you recommend?",
    "Show me beginner-friendly AI courses",
    "What are the top AI tools for small businesses?",
    "I'm looking for free AI image generators",
    "Help me find AI tools for coding assistance"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex flex-col items-center justify-center mb-6">
            <div className="mb-4">
              <AIventoryLogo size="xl" showText={true} />
            </div>
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Chat</h1>
              <p className="text-gray-600">Your intelligent AI assistant</p>
            </div>
          </div>
          
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Get personalized AI tool recommendations, discover new resources, and find exactly what you need 
            through our intelligent chat interface.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/login">
              <Button size="lg" className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200">
                <MessageCircle className="w-5 h-5 mr-2" />
                Start Chatting
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
            <Link href="/browse">
              <Button variant="outline" size="lg" className="px-8 py-3 rounded-xl border-2 hover:bg-gray-50">
                Browse Tools Instead
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-200 bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <feature.icon className="w-6 h-6 text-indigo-600" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-center text-gray-600">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sample Questions */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-3">Try asking me...</h2>
            <p className="text-gray-600">Here are some examples of what you can ask once you log in:</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sampleQuestions.map((question, index) => (
              <div 
                key={index}
                className="p-4 bg-gradient-to-br from-gray-50 to-gray-100/50 rounded-xl border border-gray-200/50 hover:shadow-md transition-all duration-200 cursor-pointer group"
              >
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                    <MessageCircle className="w-4 h-4 text-white" />
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">{question}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white shadow-xl">
            <Shield className="w-12 h-12 mx-auto mb-4 opacity-90" />
            <h3 className="text-2xl font-semibold mb-3">Ready to get started?</h3>
            <p className="text-indigo-100 mb-6 max-w-md mx-auto">
              Join thousands of users who are already discovering amazing AI tools through our intelligent assistant.
            </p>
            <Link href="/login">
              <Button size="lg" variant="secondary" className="bg-white text-indigo-600 hover:bg-gray-50 px-8 py-3 rounded-xl font-semibold">
                Sign In to Start Chatting
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPrompt;
