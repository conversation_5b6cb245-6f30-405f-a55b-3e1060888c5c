import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity } from '@/types/entity';
import { Star, ArrowRight, Share2, ExternalLink, Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useShare } from '@/hooks/useShare';
import UpvoteButton from '@/components/common/UpvoteButton';

interface ResourceCardProps {
  entity: Entity;
  onBookmark?: (entityId: string) => void;
  onShare?: (entity: Entity) => void;
}

const ResourceCard: React.FC<ResourceCardProps> = React.memo(({
  entity,
  onBookmark,
  onShare,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false);

  const { session } = useAuth();
  const router = useRouter();
  const { isBookmarked, toggleBookmark } = useBookmarkContext();
  const { shareEntity } = useShare();

  // Move all hooks before any early returns
  const fallbackImage = '/images/placeholder-logo.png';

  // Memoize the image source to prevent flashing
  const imageSrc = React.useMemo(() => {
    if (!entity) return fallbackImage;
    return imageError ? fallbackImage : (entity?.logoUrl || fallbackImage);
  }, [imageError, entity, fallbackImage]);

  if (!entity) {
    console.error("ResourceCard - Received undefined entity prop. Rendering null.");
    return null;
  }

  // Debug logging for recommendation entities
  console.log('ResourceCard - Received entity:', entity);
  console.log('ResourceCard - Entity structure check:', {
    id: entity.id,
    name: entity.name,
    slug: entity.slug,
    logoUrl: entity.logoUrl,
    shortDescription: entity.shortDescription,
    entityType: entity.entityType,
    hasFreeTier: entity.hasFreeTier
  });

  const entityName = entity?.name || 'Unnamed Entity';
  const entityDescription = entity?.shortDescription || entity?.description || 'No description available.';
  const entityTypeName = entity?.entityType?.name || 'N/A';
  const averageRating = typeof entity?.avgRating === 'number' ? entity.avgRating.toFixed(1) : 'N/A';
  const reviewCount = typeof entity?.reviewCount === 'number' ? entity.reviewCount : 0;

  const handleBookmark = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!session) {
      // Redirect to login if not authenticated
      router.push('/login');
      return;
    }

    setIsBookmarkLoading(true);
    try {
      await toggleBookmark(entity.id);
      onBookmark?.(entity.id); // Call the optional callback if provided
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      // You could show a toast notification here
    } finally {
      setIsBookmarkLoading(false);
    }
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      await shareEntity(entity);
      onShare?.(entity); // Call the optional callback if provided
    } catch (error) {
      console.error('Failed to share entity:', error);
      // You could show a toast notification here
    }
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  return (
    <div
      data-testid="resource-card"
      className="group relative bg-white border border-gray-100 rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-200 hover:-translate-y-1 flex flex-col h-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Section */}
      <Link href={`/entities/${entity?.slug || entity?.id || '#'}`} className="block">
        <div className="relative w-full h-48 bg-gradient-to-br from-indigo-50 to-purple-50 overflow-hidden">
          {!imageLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-indigo-200 border-t-indigo-600 rounded-full animate-spin"></div>
            </div>
          )}
          <Image
            src={imageSrc}
            alt={`${entityName} logo`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{ objectFit: "contain" }}
            className={`p-6 transition-all duration-200 group-hover:scale-105 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onError={handleImageError}
            onLoad={handleImageLoad}
            priority={false}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
          />

          {/* Quick Actions Overlay - Visible on Hover */}
          <div className={`absolute top-3 right-3 flex gap-2 transition-opacity duration-200 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background"
              onClick={handleBookmark}
              disabled={isBookmarkLoading}
              aria-label={isBookmarked(entity.id) ? "Remove bookmark" : "Add bookmark"}
            >
              {isBookmarkLoading ? (
                <div className="w-4 h-4 border border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
              ) : (
                <Bookmark className={`h-4 w-4 ${isBookmarked(entity.id) ? 'fill-current text-primary' : ''}`} />
              )}
            </Button>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background"
              onClick={handleShare}
              aria-label="Share resource"
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Link>

      {/* Content Section */}
      <div className="p-6 flex flex-col flex-grow">
        <Link href={`/entities/${entity.slug || entity.id}`}>
          <h3 className="text-lg font-semibold mb-3 text-card-foreground hover:text-primary transition-colors line-clamp-2" title={entityName}>
            {entityName}
          </h3>
        </Link>

        {/* Type and Category Badges */}
        <div className="mb-4 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-3 py-1 text-xs font-medium bg-indigo-100 text-indigo-700 rounded-full">
            {entityTypeName}
          </span>
          {entity?.categories && entity.categories.length > 0 && (
            <span className="inline-flex items-center px-3 py-1 text-xs font-medium bg-amber-100 text-amber-700 rounded-full">
              {entity.categories[0].name}
              {entity.categories.length > 1 && ` +${entity.categories.length - 1}`}
            </span>
          )}
          {entity?.hasFreeTier && (
            <span className="inline-flex items-center px-3 py-1 text-xs font-medium bg-green-100 text-green-700 rounded-full">
              Free Tier
            </span>
          )}
        </div>

        {/* Description */}
        <p className="text-muted-foreground text-sm mb-4 line-clamp-3 flex-grow" title={entityDescription}>
          {entityDescription}
        </p>

        {/* Footer Section */}
        <div className="mt-auto space-y-4">
          {/* Rating and Stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-secondary fill-current" />
                <span className="text-sm font-medium text-card-foreground">
                  {averageRating}
                </span>
                <span className="text-xs text-muted-foreground">
                  ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
                </span>
              </div>

              {/* Upvote Button */}
              <UpvoteButton
                entityId={entity.id}
                initialUpvoteCount={entity.upvoteCount || 0}
                size="sm"
                variant="compact"
              />
            </div>

            {/* External Link Indicator */}
            {entity?.websiteUrl && (
              <ExternalLink className="w-4 h-4 text-muted-foreground" />
            )}
          </div>

          {/* Features and Tags */}
          {((entity?.features && entity.features.length > 0) || (entity?.tags && entity.tags.length > 0)) && (
            <div className="flex flex-wrap gap-1">
              {/* Show top features first */}
              {entity?.features?.slice(0, 2).map((feature) => (
                <span
                  key={feature?.id || Math.random()}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-700 rounded-md"
                >
                  {feature?.name || 'Unnamed Feature'}
                </span>
              ))}
              {/* Then show tags */}
              {entity?.tags?.slice(0, entity?.features?.length > 0 ? 1 : 3).map((tag) => (
                <span
                  key={tag?.id || Math.random()}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md"
                >
                  {tag?.name || 'Unnamed Tag'}
                </span>
              ))}
              {/* Show count of remaining items */}
              {((entity?.features?.length || 0) + (entity?.tags?.length || 0)) > 3 && (
                <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md">
                  +{((entity?.features?.length || 0) + (entity?.tags?.length || 0)) - 3} more
                </span>
              )}
            </div>
          )}

          {/* CTA Button */}
          <Link
            href={`/entities/${entity.slug || entity.id}`}
            className="inline-flex items-center justify-center w-full px-4 py-3 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors duration-150 group"
          >
            View Details
            <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-150 group-hover:translate-x-0.5" />
          </Link>
        </div>
      </div>
    </div>
  );
});

ResourceCard.displayName = 'ResourceCard';

export default ResourceCard;