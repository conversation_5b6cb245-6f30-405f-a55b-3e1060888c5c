import React from 'react';
import Image from 'next/image';

interface AIventoryLogoProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  showText?: boolean;
  variant?: 'horizontal' | 'stacked' | 'icon-only' | 'minimal';
  theme?: 'light' | 'dark';
}

const AIventoryLogo: React.FC<AIventoryLogoProps> = ({
  size = 'md',
  className = '',
  showText = true,
  variant = 'horizontal',
  theme = 'light'
}) => {
  // Enhanced size system with better scaling
  const sizeConfig = {
    xs: { text: 'text-xs', icon: 16, spacing: 'gap-1', padding: 'px-2 py-1' },
    sm: { text: 'text-sm', icon: 20, spacing: 'gap-1.5', padding: 'px-2.5 py-1.5' },
    md: { text: 'text-base', icon: 24, spacing: 'gap-2', padding: 'px-3 py-2' },
    lg: { text: 'text-lg', icon: 28, spacing: 'gap-2.5', padding: 'px-4 py-2.5' },
    xl: { text: 'text-xl', icon: 32, spacing: 'gap-3', padding: 'px-5 py-3' },
    '2xl': { text: 'text-2xl', icon: 40, spacing: 'gap-4', padding: 'px-6 py-4' }
  };

  const config = sizeConfig[size];

  // Logo icon component
  const LogoIcon = () => (
    <div className="flex-shrink-0 relative">
      <svg
        width={config.icon}
        height={config.icon}
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="transition-transform duration-300 group-hover:scale-105"
      >
        <defs>
          <linearGradient id={`primaryGradient-${size}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6366f1" />
            <stop offset="50%" stopColor="#7c3aed" />
            <stop offset="100%" stopColor="#8b5cf6" />
          </linearGradient>
          <linearGradient id={`accentGradient-${size}`} x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f59e0b" />
            <stop offset="100%" stopColor="#f97316" />
          </linearGradient>
        </defs>

        {/* Background circle */}
        <circle
          cx="20"
          cy="20"
          r="19"
          fill={theme === 'dark' ? '#1f2937' : '#ffffff'}
          stroke={theme === 'dark' ? '#374151' : '#e2e8f0'}
          strokeWidth="1"
        />

        {/* Compass icon */}
        <g transform="translate(20, 20)">
          <circle cx="0" cy="0" r="12" fill="none" stroke={`url(#primaryGradient-${size})`} strokeWidth="1.5" opacity="0.3"/>
          <path d="M0,-8 L2,-4 L0,-2 L-2,-4 Z" fill={`url(#primaryGradient-${size})`}/>
          <path d="M0,8 L2,4 L0,2 L-2,4 Z" fill={`url(#primaryGradient-${size})`} opacity="0.6"/>
          <path d="M8,0 L4,2 L2,0 L4,-2 Z" fill={`url(#accentGradient-${size})`} opacity="0.8"/>
          <path d="M-8,0 L-4,2 L-2,0 L-4,-2 Z" fill={`url(#accentGradient-${size})`} opacity="0.6"/>
          <circle cx="0" cy="0" r="2" fill={`url(#primaryGradient-${size})`}/>
          <circle cx="0" cy="0" r="6" fill="none" stroke={`url(#primaryGradient-${size})`} strokeWidth="0.5" opacity="0.2" strokeDasharray="2,2"/>
          <circle cx="0" cy="0" r="9" fill="none" stroke={`url(#accentGradient-${size})`} strokeWidth="0.5" opacity="0.15" strokeDasharray="3,3"/>
        </g>
      </svg>
    </div>
  );

  // Enhanced wordmark with better typography
  const Wordmark = () => (
    <div className="flex flex-col">
      <span className={`font-bold ${config.text} leading-tight tracking-tight transition-all duration-300`}>
        <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 bg-clip-text text-transparent">
          AI
        </span>
        <span className={`${theme === 'dark' ? 'text-white' : 'text-gray-900'} ml-0.5`}>
          ventory
        </span>
      </span>
      {size === 'xl' || size === '2xl' ? (
        <span className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} font-medium tracking-wide mt-0.5`}>
          AI RESOURCE PLATFORM
        </span>
      ) : null}
    </div>
  );

  // Horizontal layout (default)
  const HorizontalLogo = () => (
    <div className={`group inline-flex items-center cursor-pointer transition-all duration-300 ${config.spacing}`}>
      <LogoIcon />
      {showText && <Wordmark />}
    </div>
  );

  // Stacked layout for square contexts
  const StackedLogo = () => (
    <div className="group inline-flex flex-col items-center cursor-pointer transition-all duration-300 gap-2">
      <LogoIcon />
      {showText && <Wordmark />}
    </div>
  );

  // Icon only version
  const IconOnlyLogo = () => (
    <div className="group inline-flex items-center cursor-pointer transition-all duration-300">
      <LogoIcon />
    </div>
  );

  // Minimal text-only version
  const MinimalLogo = () => (
    <div className="group inline-flex items-center cursor-pointer transition-all duration-300">
      <span className={`font-bold ${config.text} bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 bg-clip-text text-transparent tracking-tight`}>
        AI
      </span>
    </div>
  );

  const renderLogo = () => {
    switch (variant) {
      case 'stacked':
        return <StackedLogo />;
      case 'icon-only':
        return <IconOnlyLogo />;
      case 'minimal':
        return <MinimalLogo />;
      case 'horizontal':
      default:
        return <HorizontalLogo />;
    }
  };

  return (
    <div
      className={`select-none ${className}`}
      role="img"
      aria-label="AIventory - AI Resource Platform"
    >
      {renderLogo()}
    </div>
  );
};

export default AIventoryLogo;
