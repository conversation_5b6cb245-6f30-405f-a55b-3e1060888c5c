import React from 'react';
import Link from 'next/link';
import AIventoryLogo from '@/components/ui/AIventoryLogo';

const Footer = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 text-gray-700 py-8 mt-auto">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center space-y-4">
          {/* Logo */}
          <Link href="/" className="hover:opacity-80 transition-opacity duration-200">
            <AIventoryLogo
              size="sm"
              variant="horizontal"
              showText={true}
            />
          </Link>

          {/* Copyright */}
          <p className="text-sm text-gray-500">
            &copy; {new Date().getFullYear()} AIventory. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 